"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/submit-job/page",{

/***/ "(app-pages-browser)/./src/types/models.ts":
/*!*****************************!*\
  !*** ./src/types/models.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JobCategory: function() { return /* binding */ JobCategory; },\n/* harmony export */   JobStatus: function() { return /* binding */ JobStatus; },\n/* harmony export */   JobType: function() { return /* binding */ JobType; },\n/* harmony export */   OutputFormat: function() { return /* binding */ OutputFormat; },\n/* harmony export */   UserRole: function() { return /* binding */ UserRole; }\n/* harmony export */ });\nvar JobType;\n(function(JobType) {\n    JobType[\"DataEntry\"] = \"DataEntry\";\n    JobType[\"Accounting\"] = \"Accounting\";\n    JobType[\"HR\"] = \"HR\";\n    JobType[\"ITSupport\"] = \"ITSupport\";\n    JobType[\"Marketing\"] = \"Marketing\";\n    JobType[\"Legal\"] = \"Legal\";\n    JobType[\"CustomerService\"] = \"CustomerService\";\n    JobType[\"Other\"] = \"Other\";\n})(JobType || (JobType = {}));\nvar JobCategory;\n(function(JobCategory) {\n    // Data Entry Categories\n    JobCategory[\"DataProcessing\"] = \"DataProcessing\";\n    JobCategory[\"DataCleaning\"] = \"DataCleaning\";\n    JobCategory[\"DocumentationEntry\"] = \"DocumentationEntry\";\n    // Accounting Categories\n    JobCategory[\"Bookkeeping\"] = \"Bookkeeping\";\n    JobCategory[\"FinancialReporting\"] = \"FinancialReporting\";\n    JobCategory[\"Taxation\"] = \"Taxation\";\n    JobCategory[\"Payroll\"] = \"Payroll\";\n    // HR Categories\n    JobCategory[\"Recruitment\"] = \"Recruitment\";\n    JobCategory[\"EmployeeRelations\"] = \"EmployeeRelations\";\n    JobCategory[\"Training\"] = \"Training\";\n    JobCategory[\"CompensationBenefits\"] = \"CompensationBenefits\";\n    // IT Support Categories\n    JobCategory[\"TechnicalSupport\"] = \"TechnicalSupport\";\n    JobCategory[\"NetworkSupport\"] = \"NetworkSupport\";\n    JobCategory[\"SoftwareSupport\"] = \"SoftwareSupport\";\n    JobCategory[\"HardwareSupport\"] = \"HardwareSupport\";\n    // Marketing Categories\n    JobCategory[\"DigitalMarketing\"] = \"DigitalMarketing\";\n    JobCategory[\"ContentCreation\"] = \"ContentCreation\";\n    JobCategory[\"SocialMedia\"] = \"SocialMedia\";\n    JobCategory[\"MarketResearch\"] = \"MarketResearch\";\n    // Legal Categories\n    JobCategory[\"ContractReview\"] = \"ContractReview\";\n    JobCategory[\"Compliance\"] = \"Compliance\";\n    JobCategory[\"LegalResearch\"] = \"LegalResearch\";\n    JobCategory[\"Documentation\"] = \"Documentation\";\n    // Customer Service Categories\n    JobCategory[\"CallCenter\"] = \"CallCenter\";\n    JobCategory[\"EmailSupport\"] = \"EmailSupport\";\n    JobCategory[\"ChatSupport\"] = \"ChatSupport\";\n    JobCategory[\"CustomerFeedback\"] = \"CustomerFeedback\";\n    // Other\n    JobCategory[\"Other\"] = \"Other\";\n})(JobCategory || (JobCategory = {}));\nvar OutputFormat;\n(function(OutputFormat) {\n    OutputFormat[\"PDF\"] = \"PDF\";\n    OutputFormat[\"Word\"] = \"Word\";\n    OutputFormat[\"Excel\"] = \"Excel\";\n    OutputFormat[\"PlainText\"] = \"PlainText\";\n    OutputFormat[\"JSON\"] = \"JSON\";\n    OutputFormat[\"XML\"] = \"XML\";\n    OutputFormat[\"Database\"] = \"Database\";\n    OutputFormat[\"Other\"] = \"Other\";\n})(OutputFormat || (OutputFormat = {}));\nvar JobStatus;\n(function(JobStatus) {\n    JobStatus[\"New\"] = \"New\";\n    JobStatus[\"Returned\"] = \"Returned\";\n    JobStatus[\"Assigned\"] = \"Assigned\";\n    JobStatus[\"InProgress\"] = \"In Progress\";\n    JobStatus[\"AwaitingReview\"] = \"Awaiting Review\";\n    JobStatus[\"Released\"] = \"Released\";\n    JobStatus[\"Closed\"] = \"Closed\";\n})(JobStatus || (JobStatus = {}));\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"User\"] = \"User\";\n    UserRole[\"Admin\"] = \"Admin\";\n    UserRole[\"Supervisor\"] = \"Supervisor\";\n    UserRole[\"Staff\"] = \"Staff\";\n})(UserRole || (UserRole = {}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/models.ts\n"));

/***/ })

});