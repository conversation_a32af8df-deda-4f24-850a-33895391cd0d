"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/jobs/[id]/page",{

/***/ "(app-pages-browser)/./src/app/jobs/[id]/page.tsx":
/*!************************************!*\
  !*** ./src/app/jobs/[id]/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ JobDetailsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_job__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/job */ \"(app-pages-browser)/./src/lib/job.ts\");\n/* harmony import */ var _utils_statusDisplay__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/statusDisplay */ \"(app-pages-browser)/./src/utils/statusDisplay.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction JobDetailsPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const [job, setJob] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Workflow state\n    const [showCompleteModal, setShowCompleteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [outputDetails, setOutputDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const jobId = params.id ? parseInt(params.id) : null;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!jobId) {\n            setError(\"Invalid job ID\");\n            setIsLoading(false);\n            return;\n        }\n        // Get user info\n        const userStr = sessionStorage.getItem(\"user\");\n        if (userStr) {\n            setUser(JSON.parse(userStr));\n        }\n        const fetchJob = async ()=>{\n            try {\n                console.log(\"JobDetailsPage: Fetching job with ID:\", jobId);\n                const jobData = await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.getJobById)(jobId);\n                console.log(\"JobDetailsPage: Job fetched successfully:\", jobData);\n                setJob(jobData);\n            } catch (err) {\n                console.error(\"JobDetailsPage: Error fetching job:\", err);\n                const errorMessage = err instanceof Error ? err.message : \"Failed to fetch job details\";\n                setError(errorMessage);\n                if (errorMessage.includes(\"Authentication required\") || errorMessage.includes(\"401\")) {\n                    router.push(\"/login\");\n                }\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchJob();\n    }, [\n        jobId,\n        router\n    ]);\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    // Workflow action handlers\n    const handleCompleteJob = async ()=>{\n        if (!job || !outputDetails.trim()) return;\n        setIsSubmitting(true);\n        try {\n            await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.completeJob)(job.id, outputDetails.trim());\n            // Refresh job data\n            const updatedJob = await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.getJobById)(job.id);\n            setJob(updatedJob);\n            setShowCompleteModal(false);\n            setOutputDetails(\"\");\n        } catch (err) {\n            console.error(\"Error completing job:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to complete job\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleReviewJob = async ()=>{\n        if (!job) return;\n        setIsSubmitting(true);\n        try {\n            await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.reviewCompletedJob)(job.id);\n            // Refresh job data\n            const updatedJob = await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.getJobById)(job.id);\n            setJob(updatedJob);\n        } catch (err) {\n            console.error(\"Error reviewing job:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to review job\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleMarkSatisfied = async ()=>{\n        if (!job) return;\n        setIsSubmitting(true);\n        try {\n            await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.markJobSatisfied)(job.id);\n            // Refresh job data\n            const updatedJob = await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.getJobById)(job.id);\n            setJob(updatedJob);\n        } catch (err) {\n            console.error(\"Error marking job as satisfied:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to mark job as satisfied\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Determine available actions based on user role and job status\n    const getAvailableActions = ()=>{\n        if (!job || !user) return [];\n        const actions = [];\n        // Staff actions\n        if (user.role === \"Staff\" && job.status === \"InProgress\") {\n            actions.push({\n                type: \"complete\",\n                label: \"Complete Job\",\n                color: \"emerald\",\n                action: ()=>setShowCompleteModal(true)\n            });\n        }\n        // Supervisor actions\n        if (user.role === \"Supervisor\" && job.status === \"AwaitingReview\") {\n            actions.push({\n                type: \"review\",\n                label: \"Review & Deliver\",\n                color: \"blue\",\n                action: handleReviewJob\n            });\n        }\n        // Client actions\n        if (user.role === \"User\" && job.status === \"Released\") {\n            actions.push({\n                type: \"satisfy\",\n                label: \"Mark as Satisfied\",\n                color: \"green\",\n                action: handleMarkSatisfied\n            });\n        }\n        return actions;\n    };\n    const getStatusBadge = (status)=>{\n        const displayText = (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_4__.getRoleBasedStatusDisplay)(status, (user === null || user === void 0 ? void 0 : user.role) || \"User\");\n        const colors = (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_4__.getStatusColors)(status);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            style: {\n                backgroundColor: colors.bg,\n                color: colors.text,\n                border: \"1px solid \".concat(colors.border),\n                padding: \"4px 12px\",\n                borderRadius: \"9999px\",\n                fontSize: \"12px\",\n                fontWeight: \"500\"\n            },\n            children: displayText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, this);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"fixed top-0 left-0 right-0 bg-white shadow-sm z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between h-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-emerald-600\",\n                                        children: \"Staff Hall\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"pt-24 py-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-4 text-gray-600\",\n                                        children: \"Loading job details...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"fixed top-0 left-0 right-0 bg-white shadow-sm z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between h-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-emerald-600\",\n                                        children: \"Staff Hall\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"pt-24 py-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-md p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 text-red-400\",\n                                            viewBox: \"0 0 20 20\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-red-800\",\n                                                children: \"Error\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 text-sm text-red-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>router.push(\"/dashboard\"),\n                                                    className: \"bg-emerald-100 hover:bg-emerald-200 text-emerald-800 px-4 py-2 rounded-md text-sm font-medium\",\n                                                    children: \"Back to Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n            lineNumber: 213,\n            columnNumber: 7\n        }, this);\n    }\n    if (!job) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"fixed top-0 left-0 right-0 bg-white shadow-sm z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between h-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-emerald-600\",\n                                        children: \"Staff Hall\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"pt-24 py-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Job not found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/dashboard\"),\n                                    className: \"mt-4 bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium\",\n                                    children: \"Back to Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n            lineNumber: 260,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 left-0 right-0 bg-white shadow-sm z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-emerald-600\",\n                                            children: \"Staff Hall\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:ml-6 sm:flex sm:space-x-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/dashboard\",\n                                                className: \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\",\n                                                children: \"Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/submit-job\",\n                                                className: \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\",\n                                                children: \"Submit Job\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/buy-hours\",\n                                                className: \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\",\n                                                children: \"Buy Hours\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500\",\n                                    onClick: ()=>{\n                                        sessionStorage.clear();\n                                        document.cookie = \"token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;\";\n                                        window.location.href = \"/login\";\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Sign Out\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-24 py-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/dashboard\"),\n                                    className: \"mb-4 text-emerald-600 hover:text-emerald-800 text-sm font-medium flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 mr-1\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Back to Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900\",\n                                            children: job.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 15\n                                        }, this),\n                                        getStatusBadge(job.status)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow overflow-hidden sm:rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 py-5 sm:px-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg leading-6 font-medium text-gray-900\",\n                                            children: \"Job Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 max-w-2xl text-sm text-gray-500\",\n                                            children: \"Complete details about this job.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Job ID\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: [\n                                                            \"#\",\n                                                            job.id\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: job.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Job Type\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: job.jobType\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Category\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: job.category\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Output Format\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: job.outputFormat\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: getStatusBadge(job.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Created\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: formatDate(job.createdAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this),\n                                            job.updatedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Last Updated\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: formatDate(job.updatedAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 19\n                                            }, this),\n                                            job.attachmentUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Attachment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: job.attachmentUrl,\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"text-emerald-600 hover:text-emerald-800 underline\",\n                                                            children: \"View Attachment\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 19\n                                            }, this),\n                                            job.referenceUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Reference URL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: job.referenceUrl,\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"text-emerald-600 hover:text-emerald-800 underline\",\n                                                            children: \"View Reference\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 19\n                                            }, this),\n                                            job.outputDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Output Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: job.outputDetails\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 19\n                                            }, this),\n                                            job.completedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Completed At\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: formatDate(job.completedAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 19\n                                            }, this),\n                                            job.deliveredAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Delivered At\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: formatDate(job.deliveredAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 19\n                                            }, this),\n                                            job.satisfiedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Satisfied At\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: formatDate(job.satisfiedAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 11\n                        }, this),\n                        getAvailableActions().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 bg-white shadow sm:rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-5 sm:p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg leading-6 font-medium text-gray-900 mb-4\",\n                                        children: \"Available Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: getAvailableActions().map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: action.action,\n                                                disabled: isSubmitting,\n                                                className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed \".concat(action.color === \"emerald\" ? \"bg-emerald-600 hover:bg-emerald-700 focus:ring-emerald-500\" : action.color === \"blue\" ? \"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500\" : action.color === \"green\" ? \"bg-green-600 hover:bg-green-700 focus:ring-green-500\" : \"bg-gray-600 hover:bg-gray-700 focus:ring-gray-500\"),\n                                                children: isSubmitting ? \"Processing...\" : action.label\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 13\n                        }, this),\n                        showCompleteModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: [\n                                                \"Complete Job: \",\n                                                job === null || job === void 0 ? void 0 : job.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"outputDetails\",\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Output Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"outputDetails\",\n                                                    rows: 4,\n                                                    value: outputDetails,\n                                                    onChange: (e)=>setOutputDetails(e.target.value),\n                                                    placeholder: \"Describe how the client can access the output (e.g., 'Document has been emailed to client', 'Files uploaded to shared folder', etc.)\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setShowCompleteModal(false);\n                                                        setOutputDetails(\"\");\n                                                    },\n                                                    className: \"flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500\",\n                                                    disabled: isSubmitting,\n                                                    children: \"Cancel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleCompleteJob,\n                                                    disabled: isSubmitting || !outputDetails.trim(),\n                                                    className: \"flex-1 px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    children: isSubmitting ? \"Completing...\" : \"Complete Job\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 340,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n        lineNumber: 292,\n        columnNumber: 5\n    }, this);\n}\n_s(JobDetailsPage, \"IYriDqd8s4gBAta91B9RzbMu8tI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = JobDetailsPage;\nvar _c;\n$RefreshReg$(_c, \"JobDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/jobs/[id]/page.tsx\n"));

/***/ })

});